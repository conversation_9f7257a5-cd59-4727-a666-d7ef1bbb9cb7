/*
 * main.cpp - Core implementation of the Kalista bot
 *
 * This file contains the main logic and event handlers for the Kalista plugin.
 * The bot operates through event-driven callbacks registered with the SDK,
 * responding to game events like updates, drawing, and spell casting.
 *
 * <AUTHOR>
 * @date May 26, 2025
 */

#define SDK_IMPLEMENTATION
#include "main.h"

#include "config.h"
#include "helpers.h"
#include "sdk.h"

namespace kalista {

    // This is the heart of the bot - runs every frame and handles all ability logic
    void on_update(void*) {
        if (player->is_dead() || !config->enabled)
            return;

        f32  time = get_time();
        auto mode = get_action_mode();

        // Respect our cast rate limit to prevent spam casting
        if (next_cast > time) {
            return;
        }

        // Check if any enemy champions have spear stacks - this affects our Rend reset logic
        bool someone_has_rend = false;
        for (auto& enemy : get_enemy_heroes()) {
            if (enemy->has_buff(SpellHash("KalistaExpungeMarker"))) {
                if (is_valid_target(enemy, E_RANGE)) {
                    someone_has_rend = true;
                }
            }
        }

        // Always try to secure epic monsters (<PERSON>, <PERSON>) with Ren<PERSON> if we can kill them
        for (auto& minion : get_minions()) {
            if (is_valid_minion(minion, E_RANGE) && minion->is_epic()) {
                if (calc_e_damage(minion) >= minion->health()) {
                    kalista_e->cast_self();
                }
            }
        }

        // If we have spears on enemies, kill minions with Rend to reset the cooldown
        // This lets us use Rend on enemies immediately after
        if (config->harass_use_e && kalista_e->is_ready()) {
            for (const auto& minion : get_minions()) {
                if (is_valid_minion(minion, E_RANGE)) {
                    if (calc_e_damage(minion) >= minion->health() && someone_has_rend) {
                        kalista_e->cast_self();
                    }
                }
            }
        }

        // Use Rend to kill enemy champions (either in combo mode or with auto-kill enabled)
        if (config->use_e && kalista_e->is_ready() && (mode.is(ActionMode::Combo) || config->use_e_auto)) {
            for (const auto& enemy : get_enemy_heroes()) {
                if (is_valid_target(enemy, E_RANGE)) {
                    if (calc_e_damage(enemy) >= enemy->health()) {
                        kalista_e->cast_self();
                    }
                }
            }
        }

        // Use Rend for farming when in farm modes - only if we can kill multiple minions
        // or if we're low level (early game efficiency)
        if (config->farm_use_e && kalista_e->is_ready()) {
            if (mode.is(ActionMode::Farm) || mode.is(ActionMode::FastFarm) || mode.is(ActionMode::LastHit)) {
                if (player->mana() / player->max_mana() * 100.0f < config->farm_min_mana_percent) {
                    return;
                }

                auto killable_lane_minions = 0;
                for (const auto& minion : get_minions()) {
                    if (minion->is_lane_minion() && is_valid_minion(minion, E_RANGE)) {
                        if (calc_e_damage(minion) >= minion->health()) {
                            killable_lane_minions++;
                        }
                    }
                }

                auto killable_jungle_mobs = 0;
                for (const auto& minion : get_minions()) {
                    if (minion->is_jungle_monster() && is_valid_minion(minion, E_RANGE)) {
                        if (calc_e_damage(minion) >= minion->health()) {
                            killable_jungle_mobs++;
                        }
                    }
                }

                if (killable_jungle_mobs >= 1) {
                    kalista_e->cast_self();
                }

                // Use Rend if we can kill 3+ minions, or 1+ minion if we're low level (before level 7)
                if (killable_lane_minions >= 3 || killable_lane_minions >= 1 && player->level() < 7) {
                    kalista_e->cast_self();
                }
            }
        }

        // Use Pierce in combo mode with prediction to hit moving targets
        if (config->use_q && kalista_q->is_ready() && is_mode({ActionMode::Combo, ActionMode::Harass})) {
            ts::TargetOptions options = {.range = Q_RANGE, .is_valid = nullptr};
            auto              ts      = ts::get_pred_target(options, pred_q, [](const PredResult& pred) { return pred.hit_chance >= 1; });

            if (ts.target && is_valid_target(ts.target)) {
                if (!is_attack_cooldown() || !in_range(ts.target->position(), player->auto_attack_range())) {
                    if (kalista_q->cast_position(ts.pred.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;
                    }
                }
            }
        }

        // Use Pierce in combo mode with prediction to hit moving targets
        if (config->harass_use_q && kalista_q->is_ready() && mode.is(ActionMode::Harass)) {
            if (player->mana() / player->max_mana() * 100.0f < config->harass_min_mana_percent) {
                return;
            }

            ts::TargetOptions options = {.range = Q_RANGE, .is_valid = nullptr};
            auto              ts      = ts::get_pred_target(options, pred_q, [](const PredResult& pred) { return pred.hit_chance >= 2; });

            if (ts.target && is_valid_target(ts.target)) {
                if (!is_attack_cooldown() || !in_range(ts.target->position(), player->auto_attack_range())) {
                    if (kalista_q->cast_position(ts.pred.cast_position)) {
                        next_cast = time + CAST_RATE;
                        return;
                    }
                }
            }
        }
    }

    // Called every frame to draw things on the game world (like range circles, lines, etc.)
    // Currently unused but available for future range indicators
    void on_draw_ground(void*) {
        if (player->is_dead() || !config->enabled)
            return;

        // TODO: Add range circles, trajectory lines, etc. here
    }

    // Called every frame to draw UI elements like damage indicators on health bars
    void on_draw_hud(void*) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        // Draw damage indicators on enemy health bars if enabled
        if (config->render_damage_indicator) {
            for (const auto& enemy : get_enemy_heroes()) {
                if (is_renderable(enemy)) {
                    const f32 e_damage = calc_e_damage(enemy);
                    draw_damage_indicator(e_damage, enemy);
                }
            }
        }
    }

    // Called before spells are cast to validate if they should be allowed
    // Used to prevent casting spells in bad situations
    void on_validate_and_cast_spell(OnValidateAndCastSpellhEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        auto spell = event->spell_data->spell_hash();

        // Prevent casting Pierce (Q) while we're already dashing
        if (spell == SpellHash("KalistaMysticShot")) {
            if (player->path_controller()->is_dashing()) {
                *(event->prevent) = true;
            }
        }
    }

    // Called when we start casting a spell - used for auto-kiting behavior
    void on_start_spell_cast(OnStartSpellCastEvent* event) {
        if (player->is_dead() || !config->enabled) {
            return;
        }

        // Only handle our own spell casts
        if (event->caster != player) {
            return;
        }

        // After casting Pierce (Q), automatically move towards cursor for kiting
        if (event->spell_cast_info->spell_hash() == SpellHash("KalistaMysticShot")) {
            auto pos = player->position().extend(get_world_cursor(), 300);
            move(pos);
        }
    }

    // Called when the plugin is loaded - sets up all our data and registers event handlers
    void on_load() {
        player = get_player();

        // Only activate if we're playing Kalista
        if (!player->is("Kalista")) {
            return;
        }

        // Get references to Kalista's spells for later use
        kalista_q = player->get_spell(SpellSlot::Q);  // Pierce
        kalista_e = player->get_spell(SpellSlot::E);  // Rend
        kalista_r = player->get_spell(SpellSlot::R);  // Fate's Call

        // Initialize our configuration system
        config = new Config();

        // Register all our event handlers with the SDK
        register_module("[Kurisu] Kalista",
            {.on_draw_ground                = on_draw_ground,              // World drawing
                .on_draw_hud                = on_draw_hud,                 // UI drawing
                .on_update                  = on_update,                   // Main game loop
                .on_draw_menu               = on_draw_menu,                // Configuration menu
                .on_validate_and_cast_spell = on_validate_and_cast_spell,  // Spell validation
                .on_start_spell_cast        = on_start_spell_cast});
    }
}  // namespace kalista

// Plugin entry point - called by the SDK when the DLL is loaded
ENTRY({ kalista::on_load(); });
